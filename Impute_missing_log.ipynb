import nbformat
from nbformat.v4 import new_notebook, new_code_cell, new_markdown_cell

# Cells extracted and organized from the PDF content
cells = []

# Title and intro
cells.append(new_markdown_cell("""
# Missing logs imputation — Prizm documentation

This notebook demonstrates a workflow for predicting missing well log values using machine learning algorithms with open-source Python libraries (scikit-learn, xgboost, etc.) and Cegal Prizm's Python Tool Pro. The data used is from the open-source Volve field dataset: https://www.equinor.com/energy/volve-data-sharing
"""))

# Import libraries and connect to Petrel project
cells.append(new_markdown_cell("""
## Import libraries and connect to Petrel project
"""))
cells.append(new_code_cell("""
# Basic libraries for data handling
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# Machine learning libraries
from xgboost import XGBRegressor
from lightgbm import LGBMRegressor
from catboost import CatBoostRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error

# Plotting libraries
import plotly.express as px
from cegal.welltools.plotting import CegalWellPlotter as cwp

# Prizm libraries and establish a Petrel connection
from cegalprizm.pythontool import PetrelConnection
petrel = PetrelConnection(allow_experimental=True)
print('Connected to {}'.format(petrel.get_current_project_name()))
"""))
cells.append(new_code_cell("""
import plotly.io as pio
pio.renderers.default='notebook'
"""))

# Select wells and logs
cells.append(new_markdown_cell("""
## Select wells and global well logs
"""))
cells.append(new_code_cell("""
wells = [i for i in petrel.wells]
log_names = ['GR', 'Vp', 'RHOB', 'NPHI', 'Vs']
logs = [i for i in petrel.global_well_logs if i.petrel_name in log_names]
"""))

# Get wells and log data
cells.append(new_markdown_cell("""
## Get wells and log data
"""))
cells.append(new_code_cell("""
well_data = pd.DataFrame()
for w in wells:
    df = w.logs_dataframe(logs)
    df['WELL'] = w.petrel_name
    well_data = pd.concat([well_data, df])
well_data
"""))

# Data exploration
cells.append(new_markdown_cell("""
## Data Exploration
"""))
cells.append(new_code_cell("""
well_19A = well_data[well_data['WELL']=='15/9-19 A']
cwp.plot_logs(well_19A.set_index('MD')[log_names])
"""))
cells.append(new_code_cell("""
well_data.describe().round(2)
"""))

# Remove extreme values
cells.append(new_markdown_cell("""
## Removing extreme values or invalid data
"""))
cells.append(new_code_cell("""
well_data['NPHI'] = np.where((well_data.NPHI >= 0) & (well_data.NPHI <= 1), well_data.NPHI, np.nan)
well_data['GR'] = np.where(well_data.GR <= 300, well_data.GR, np.nan)
"""))
cells.append(new_code_cell("""
well_19A = well_data[well_data['WELL']=='15/9-19 A']
cwp.plot_logs(well_19A.set_index('MD')[log_names])
"""))

# Data coverage
cells.append(new_markdown_cell("""
## Data Coverage
"""))
cells.append(new_code_cell("""
cwp.plot_coverage(well_data[log_names])
"""))
cells.append(new_code_cell("""
null_counts = well_data.groupby('WELL')[log_names].apply(lambda x: x.isnull().sum()/len(x)*100)
null_counts.sort_values('Vp')
"""))

# Data correlation
cells.append(new_markdown_cell("""
## Data correlation
"""))
cells.append(new_code_cell("""
px.imshow(well_data[log_names].corr().round(2), text_auto=True, color_continuous_scale='RdBu')
"""))

# ML workflow
cells.append(new_markdown_cell("""
## A simple ML workflow to impute missing log values
"""))
cells.append(new_code_cell("""
logs = ['MD', 'GR', 'Vp', 'RHOB', 'NPHI', 'Vs']
target = 'Vs'
"""))
cells.append(new_code_cell("""
data = well_data[logs].copy()
traindata = data[data[target].notna()]
X = traindata.drop([target], axis = 1)
Y = traindata[target]
X_inp = X.apply(lambda x: x.fillna(x.mean()), axis = 0)
X_train, X_val, Y_train, Y_val = train_test_split(X_inp, Y, test_size = 0.25, random_state = 42)
"""))
cells.append(new_code_cell("""
estimators = [
    XGBRegressor(n_estimators=300, tree_method='gpu_hist', learning_rate=0.05, early_stopping_rounds=100),
    CatBoostRegressor(task_type='GPU', early_stopping_rounds=100),
    LGBMRegressor(device='gpu', gpu_platform_id=1, gpu_device_id=0, n_estimators=300)
]
estimator_names = ['EXTREME BOOST REGRESSOR', 'CATBOOST REGRESSOR', 'LGBM REGRESSOR']
"""))
cells.append(new_code_cell("""
i = 0
rme_errors = []
for estimator in estimators:
    print('- Model: {}'.format(estimator_names[i]))
    estimator.fit(X_train, Y_train.values.ravel(), eval_set=[(X_val, Y_val)], verbose=0)
    train_pred = estimator.predict(X_train)
    val_pred = estimator.predict(X_val)
    mabs_error_train = mean_absolute_error(Y_train, train_pred)
    mabs_error_val = mean_absolute_error(Y_val, val_pred)
    rme_errors.append(mabs_error_val)
    print('-- Mean absolute error for validation data: ' + str(round(mabs_error_val,3)))
    i += 1
"""))
cells.append(new_code_cell("""
selected_model_index = rme_errors.index(min(rme_errors))
print('Best model for imputation: {}'.format(estimator_names[selected_model_index]))
model = estimators[selected_model_index]
model.fit(X_train, Y_train.values.ravel(), eval_set=[(X_val, Y_val)], verbose=0)
"""))

# Add predictions to well data
cells.append(new_markdown_cell("""
## Add predictions to well data
"""))
cells.append(new_code_cell("""
X_train_log = data.drop([target], axis=1)
X_train_log2 = X_train_log.apply(lambda x: x.fillna(x.mean()), axis=0)
results = data.copy()
results.loc[:, target + '_predicted'] = model.predict(X_train_log2)
results[target + '_combined'] = results[target]
results[target + '_combined'].fillna(results[target + '_predicted'], inplace=True)
results
"""))

# Function for data augmentation
cells.append(new_markdown_cell("""
## Putting it all together: Data augmentation function
"""))
cells.append(new_code_cell("""
def data_augmentation(dataset, logs, targets):
    data = dataset[logs]
    estimators = [
        XGBRegressor(n_estimators=300, tree_method='gpu_hist', learning_rate=0.05, early_stopping_rounds=100),
        CatBoostRegressor(task_type='GPU', early_stopping_rounds=100),
        LGBMRegressor(device='gpu', gpu_platform_id=1, gpu_device_id=0, n_estimators=300)
    ]
    estimator_names = ['EXTREME BOOST REGRESSOR', 'CATBOOST REGRESSOR', 'LGBM REGRESSOR']
    results = dataset.copy()
    for target in targets:
        print('---- Predicting values for: {} ----'.format(target))
        traindata = data[data[target].notna()]
        X = traindata.drop([target], axis=1)
        Y = traindata[target]
        X_inp = X.apply(lambda x: x.fillna(x.mean()), axis=0)
        X_train, X_val, Y_train, Y_val = train_test_split(X_inp, Y, test_size=0.25, random_state=42)
        i = 0
        rme_errors = []
        for estimator in estimators:
            print('- Model: {}'.format(estimator_names[i]))
            estimator.fit(X_train, Y_train.values.ravel(), eval_set=[(X_val, Y_val)], verbose=0)
            train_pred = estimator.predict(X_train)
            val_pred = estimator.predict(X_val)
            mabs_error_train = mean_absolute_error(Y_train, train_pred)
            mabs_error_val = mean_absolute_error(Y_val, val_pred)
            rme_errors.append(mabs_error_val)
            print('-- Mean absolute error for validation data: ' + str(round(mabs_error_val,3)))
            i += 1
        selected_model_index = rme_errors.index(min(rme_errors))
        print('Best model for imputation: {}'.format(estimator_names[selected_model_index]))
        model = estimators[selected_model_index]
        model.fit(X_train, Y_train.values.ravel(), eval_set=[(X_val, Y_val)], verbose=0)
        X_train_log = data.drop([target], axis=1)
        X_train_log2 = X_train_log.apply(lambda x: x.fillna(x.mean()), axis=0)
        results.loc[:, target + '_predicted'] = model.predict(X_train_log2)
        results[target + '_combined'] = results[target]
        results[target + '_combined'].fillna(results[target + '_predicted'], inplace=True)
    return results, model
"""))

# Example usage
cells.append(new_code_cell("""
results, model = data_augmentation(well_data, ['GR', 'Vp', 'RHOB', 'NPHI', 'Vs', 'MD'], ['Vs', 'Vp'])
"""))
cells.append(new_code_cell("""
results['Vp_error'] = abs(results.Vp_combined - results.Vp_predicted)/results.Vp_combined
results['Vs_error'] = abs(results.Vs_combined - results.Vs_predicted)/results.Vs_combined
"""))

# Evaluation of results
cells.append(new_markdown_cell("""
## Evaluation of results
"""))
cells.append(new_code_cell("""
fig = px.ecdf(results[results['Vs'].notna()], x="Vs_error")
fig.show()
"""))
cells.append(new_code_cell("""
fig = px.ecdf(results[results['Vp'].notna()].sample(frac=0.5), x="Vp_error")
fig.show()
"""))

# Example well plots
cells.append(new_markdown_cell("""
## Well with complete data
"""))
cells.append(new_code_cell("""
plot_logs=['GR','NPHI', 'RHOB','Vs', 'Vp', 'Vp_error', 'Vp_predicted']
single_well = results[results['WELL']=='15/9-19 BT2']
cwp.plot_logs(single_well.set_index('MD')[plot_logs])
"""))
cells.append(new_markdown_cell("""
## Well sections with limited supporting data
"""))
cells.append(new_code_cell("""
plot_logs=['GR','NPHI', 'RHOB','Vs', 'Vp', 'Vp_error', 'Vp_predicted']
single_well = results[results['WELL']=='15/9-F-14']
cwp.plot_logs(single_well.set_index('MD')[plot_logs])
"""))
cells.append(new_markdown_cell("""
## Well without Vp
"""))
cells.append(new_code_cell("""
plot_logs=['GR','NPHI', 'RHOB', 'Vp_predicted']
single_well = results[results['WELL']=='15/9-F-10']
cwp.plot_logs(single_well.set_index('MD')[plot_logs])
"""))

# Write back to Petrel
cells.append(new_markdown_cell("""
## Write the results back to Petrel
"""))
cells.append(new_code_cell("""
def write_back(wells, vae_logs, clone, well_data):
    for w in wells:
        print(w.petrel_name)
        if len([log for log in w.logs if log.petrel_name == clone]) == 0:
            log_to_clone = [log for log in w.logs][0]
        else:
            log_to_clone = [log for log in w.logs if log.petrel_name == clone][0]
        current_well_df = well_data.loc[well_data['WELL'] == w.petrel_name]
        current_well_df = current_well_df.set_index('MD')
        md = current_well_df.index.to_numpy()
        vae_logs_in_petrel = [i for i in petrel.global_well_logs if i.petrel_name in vae_logs]
        # If none of the new logs are in the project we need to create global well logs
        if len(vae_logs_in_petrel) == 0:
            for v in vae_logs:
                values = current_well_df[v].to_numpy()
                j = log_to_clone.clone(v, copy_values=False)
                j.set_values(md, values)
        # If the new logs are in the project as global well logs already we want to update them
        else:
            for v in vae_logs:
                gwl = [l for l in petrel.global_well_logs if l.petrel_name == v][0]
                log = [log for log in w.logs if log.petrel_name == v]
                if len(log) > 0:
                    log1 = petrel.well_logs[log[0].path]
                    log1.readonly = False
                    values = current_well_df[v].to_numpy()
                    log1.set_values(md, values)
                else:
                    gwl.create_well_log(w)
                    log_to_write_to = [log for log in w.logs if log.petrel_name == v]
                    log1 = petrel.well_logs[log_to_write_to[0].path]
                    log1.readonly = False
                    values = current_well_df[v].to_numpy()
                    log1.set_values(md, values)
"""))
cells.append(new_code_cell("""
write_back(wells=wells, vae_logs=['Vs_predicted', 'Vs_combined','Vs_error'], clone='Vs', well_data=results)
"""))

# Create notebook
nb = new_notebook(cells=cells)
with open('missing_logs_imputation_prizm.ipynb', 'w', encoding='utf-8') as f:
    nbformat.write(nb, f)
"))}  !*