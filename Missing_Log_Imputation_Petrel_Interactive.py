# Start: PWR Description

from cegalprizm.pycoderunner import WorkflowDescription, DomainObjectsEnum, TemplateNamesEnum

pwr_description = WorkflowDescription(name="Missing Log Imputation - Petrel Interactive",
                                      category="Well log analysis",
                                      description="Interactive workflow for missing well log imputation using multiple ML algorithms. Select wells and logs directly from the current Petrel project, choose target log for imputation, and compare performance of different algorithms.",
                                      authors="BKP_Team@PTM",
                                      version="1.0")

# --- Define the UI for the Prizm Workflow Runner ---

pwr_description.add_object_ref_parameter(name='index_well_id', label='Index Well (Reference)', description='Select a reference well with complete log data for training', object_type=DomainObjectsEnum.Well)
pwr_description.add_multi_object_ref_parameter(name='input_wells_ids', label='Input Wells', description='Select wells to include in the analysis (including wells with missing data)', object_type=DomainObjectsEnum.Well)
pwr_description.add_multi_object_ref_parameter(name='input_logs_ids', label='Input Log Curves', description='Select the log curves to use as features for prediction', object_type=DomainObjectsEnum.GlobalWellLog)
pwr_description.add_object_ref_parameter(name='target_log_id', label='Target Log to Predict', description='Select the log curve that needs to be imputed/predicted', object_type=DomainObjectsEnum.GlobalWellLog)

pwr_description.add_enum_parameter(name='imputation_mode', label='Imputation Mode', description='Choose the imputation strategy', options={0:'Missing Values Only', 1:'Cross-Validation Test', 2:'Full Re-prediction'}, default_value=0)
pwr_description.add_float_parameter(name='test_percentage', label='Test Data Percentage', description='Percentage of complete data to use for testing (Cross-validation mode)', default_value=25.0, minimum_value=10.0, maximum_value=50.0)
pwr_description.add_boolean_parameter(name='use_depth_feature', label='Include Depth as Feature', description='Use measured depth (MD) as an additional feature for prediction', default_value=True)
pwr_description.add_boolean_parameter(name='enable_advanced_models', label='Enable Advanced Models', description='Include XGBoost, LightGBM, and CatBoost (requires additional packages)', default_value=True)
pwr_description.add_boolean_parameter(name='cross_validation', label='Use Cross Validation', description='Perform k-fold cross validation for model evaluation', default_value=True)
pwr_description.add_integer_parameter(name='cv_folds', label='CV Folds', description='Number of cross-validation folds', default_value=5, minimum_value=3, maximum_value=10)
pwr_description.add_boolean_parameter(name='create_output_logs', label='Create Output Logs', description='Create new well logs in Petrel with imputed values', default_value=True)
pwr_description.add_string_parameter(name='output_suffix', label='Output Log Suffix', description='Suffix to add to output log names (e.g., "_imputed")', default_value='_ML_imputed')
pwr_description.add_boolean_parameter(name='generate_plots', label='Generate Plots', description='Create visualization plots for analysis', default_value=True)

# End: PWR Description


# Core libraries
import numpy as np
import pandas as pd
import warnings
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any
import time
from datetime import datetime
warnings.filterwarnings('ignore')

# Try to import seaborn, use matplotlib if not available
try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False
    print("Seaborn not available. Using matplotlib for plotting.")

# Machine learning libraries
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.impute import SimpleImputer

# Advanced ML libraries (optional)
try:
    from xgboost import XGBRegressor
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    from lightgbm import LGBMRegressor
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

# Petrel connection
from cegalprizm.pythontool import PetrelConnection, Well, GlobalWellLog, WellLog


class PetrelDataLoader:
    """Load and process well log data from Petrel project"""
    
    def __init__(self, petrel_connection: PetrelConnection):
        self.petrel = petrel_connection
        self.wells_data = {}
        self.combined_data = None
        
    def load_wells_and_logs(self, well_ids: List[str], log_ids: List[str], 
                           target_log_id: str, use_depth: bool = True) -> pd.DataFrame:
        """Load well log data from selected wells and logs"""
        
        print("Loading data from Petrel project...")
        
        # Get well objects
        petrel_objects = self.petrel.get_petrelobjects_by_guids(well_ids + log_ids + [target_log_id])
        
        wells = [obj for obj in petrel_objects if isinstance(obj, Well)]
        input_logs = [obj for obj in petrel_objects if obj.guid in log_ids and isinstance(obj, GlobalWellLog)]
        target_log = next((obj for obj in petrel_objects if obj.guid == target_log_id), None)
        
        if not wells:
            raise ValueError("No valid wells selected")
        if not input_logs:
            raise ValueError("No valid input logs selected")
        if target_log is None:
            raise ValueError("No valid target log selected")
        
        print(f"  Selected wells: {len(wells)}")
        print(f"  Input logs: {[log.petrel_name for log in input_logs]}")
        print(f"  Target log: {target_log.petrel_name}")
        
        # Combine input logs and target log
        all_logs = input_logs + [target_log]
        
        # Load data from each well
        all_well_data = []
        
        for well in wells:
            print(f"  Loading data from well: {well.petrel_name}")
            
            try:
                # Get logs dataframe for this well
                well_df = well.logs_dataframe(all_logs)
                
                if well_df.empty:
                    print(f"    Warning: No data found for well {well.petrel_name}")
                    continue
                
                # Add well identifier
                well_df['WELL'] = well.petrel_name
                
                # Reset index to make MD a column
                well_df = well_df.reset_index()
                
                # Rename columns to match log names
                log_name_mapping = {}
                for i, log in enumerate(all_logs):
                    if i < len(well_df.columns) - 2:  # Exclude WELL and MD columns
                        old_name = well_df.columns[i]
                        log_name_mapping[old_name] = log.petrel_name
                
                well_df = well_df.rename(columns=log_name_mapping)
                
                all_well_data.append(well_df)
                
            except Exception as e:
                print(f"    Error loading data from well {well.petrel_name}: {str(e)}")
                continue
        
        if not all_well_data:
            raise ValueError("No data could be loaded from any wells")
        
        # Combine all well data
        combined_df = pd.concat(all_well_data, ignore_index=True)
        
        # Clean up column names and ensure we have the expected columns
        expected_columns = ['MD', 'WELL'] + [log.petrel_name for log in all_logs]
        
        # Filter to only include expected columns that exist
        available_columns = [col for col in expected_columns if col in combined_df.columns]
        combined_df = combined_df[available_columns]
        
        # Remove depth feature if not requested
        if not use_depth and 'MD' in combined_df.columns:
            combined_df = combined_df.drop('MD', axis=1)
        
        print(f"  Total samples loaded: {len(combined_df)}")
        print(f"  Available columns: {list(combined_df.columns)}")
        
        # Store for later use
        self.combined_data = combined_df
        
        return combined_df
    
    def get_data_coverage_stats(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate data coverage statistics"""
        
        coverage_stats = {}
        log_columns = [col for col in df.columns if col not in ['WELL', 'MD']]
        
        for col in log_columns:
            coverage = (1 - df[col].isna().mean()) * 100
            coverage_stats[col] = coverage
        
        return coverage_stats
    
    def identify_index_well_data(self, df: pd.DataFrame, index_well_id: str, 
                                target_log_name: str) -> pd.DataFrame:
        """Get data from the index well for training"""
        
        # Get index well object
        index_well = self.petrel.get_petrelobjects_by_guids([index_well_id])[0]
        index_well_name = index_well.petrel_name
        
        print(f"Using index well: {index_well_name}")
        
        # Filter data for index well
        index_data = df[df['WELL'] == index_well_name].copy()
        
        if index_data.empty:
            raise ValueError(f"No data found for index well: {index_well_name}")
        
        # Check if index well has complete target log data
        target_missing = index_data[target_log_name].isna().sum()
        target_total = len(index_data)
        
        print(f"  Index well {target_log_name} coverage: {((target_total - target_missing) / target_total * 100):.1f}%")
        
        if target_missing > target_total * 0.5:
            print(f"  Warning: Index well has >50% missing {target_log_name} data")
        
        return index_data


class PetrelMLImputationFramework:
    """ML framework adapted for Petrel data"""
    
    def __init__(self, enable_advanced_models: bool = True, random_seed: int = 42):
        self.enable_advanced_models = enable_advanced_models
        self.random_seed = random_seed
        self.models = {}
        self.results = {}
        
    def get_available_models(self) -> Dict[str, Any]:
        """Get dictionary of available ML models"""
        
        models = {
            'Linear_Regression': LinearRegression(),
            'Ridge_Regression': Ridge(alpha=1.0, random_state=self.random_seed),
            'Lasso_Regression': Lasso(alpha=1.0, random_state=self.random_seed),
            'Random_Forest': RandomForestRegressor(n_estimators=100, random_state=self.random_seed, n_jobs=-1),
            'Extra_Trees': ExtraTreesRegressor(n_estimators=100, random_state=self.random_seed, n_jobs=-1),
            'Gradient_Boosting': GradientBoostingRegressor(n_estimators=100, random_state=self.random_seed)
        }
        
        # Add advanced models if available and enabled
        if self.enable_advanced_models:
            if XGBOOST_AVAILABLE:
                models['XGBoost'] = XGBRegressor(n_estimators=300, learning_rate=0.05, 
                                               random_state=self.random_seed, n_jobs=-1)
            
            if LIGHTGBM_AVAILABLE:
                models['LightGBM'] = LGBMRegressor(n_estimators=300, random_state=self.random_seed, n_jobs=-1)
            
            if CATBOOST_AVAILABLE:
                models['CatBoost'] = CatBoostRegressor(iterations=300, learning_rate=0.05, 
                                                     random_state=self.random_seed, verbose=False)
        
        return models
    
    def train_and_evaluate_models(self, df: pd.DataFrame, target_col: str, 
                                 feature_cols: List[str], test_size: float = 0.25,
                                 use_cv: bool = True, cv_folds: int = 5) -> Dict[str, Dict[str, float]]:
        """Train and evaluate all available models"""
        
        print(f"Training and evaluating models for {target_col} imputation...")
        
        # Prepare data
        complete_mask = df[target_col].notna()
        train_data = df[complete_mask].copy()
        
        if len(train_data) < 10:
            raise ValueError(f"Insufficient complete data for {target_col} (only {len(train_data)} samples)")
        
        X = train_data[feature_cols].copy()
        y = train_data[target_col].values
        
        # Handle missing values in features using mean imputation
        imputer = SimpleImputer(strategy='mean')
        X_imputed = imputer.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_imputed, y, test_size=test_size, random_state=self.random_seed
        )
        
        print(f"  Training samples: {len(X_train)}")
        print(f"  Test samples: {len(X_test)}")
        print(f"  Features: {feature_cols}")
        print()
        
        # Get available models
        models = self.get_available_models()
        results = {}
        
        for model_name, model in models.items():
            print(f"  Training {model_name}...")
            start_time = time.time()
            
            try:
                # Train model
                model.fit(X_train, y_train)
                
                # Make predictions
                y_pred_train = model.predict(X_train)
                y_pred_test = model.predict(X_test)
                
                # Calculate metrics
                train_mae = mean_absolute_error(y_train, y_pred_train)
                test_mae = mean_absolute_error(y_test, y_pred_test)
                train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)
                
                # Cross-validation if requested
                cv_scores = None
                if use_cv:
                    cv_scores = cross_val_score(model, X_train, y_train, 
                                              cv=cv_folds, scoring='neg_mean_absolute_error')
                    cv_mae = -cv_scores.mean()
                    cv_std = cv_scores.std()
                else:
                    cv_mae = cv_std = None
                
                training_time = time.time() - start_time
                
                # Store results
                results[model_name] = {
                    'train_mae': train_mae,
                    'test_mae': test_mae,
                    'train_rmse': train_rmse,
                    'test_rmse': test_rmse,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'cv_mae': cv_mae,
                    'cv_std': cv_std,
                    'training_time': training_time,
                    'model': model,
                    'imputer': imputer
                }
                
                print(f"    Test MAE: {test_mae:.3f}, Test R²: {test_r2:.3f}, Time: {training_time:.2f}s")
                if use_cv:
                    print(f"    CV MAE: {cv_mae:.3f} ± {cv_std:.3f}")
                
            except Exception as e:
                print(f"    Error training {model_name}: {str(e)}")
                results[model_name] = {'error': str(e)}
        
        print()
        self.results[target_col] = results
        return results


def main():
    """Main execution function"""
    
    print("="*80)
    print("MISSING LOG IMPUTATION - PETREL INTERACTIVE")
    print("="*80)
    print(f"Execution started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Connect to Petrel
    try:
        petrel = PetrelConnection(allow_experimental=True)
        print(f'Connected to Petrel project: {petrel.get_current_project_name()}')
        print()
    except Exception as e:
        print(f"Error connecting to Petrel: {str(e)}")
        print("Make sure Petrel is running and Python Tool Pro is properly configured.")
        return
    
    # Get parameters
    index_well_id = parameters['index_well_id']
    input_wells_ids = parameters['input_wells_ids']
    input_logs_ids = parameters['input_logs_ids']
    target_log_id = parameters['target_log_id']
    
    imputation_mode = parameters['imputation_mode']
    test_percentage = parameters['test_percentage']
    use_depth_feature = parameters['use_depth_feature']
    enable_advanced_models = parameters['enable_advanced_models']
    cross_validation = parameters['cross_validation']
    cv_folds = parameters['cv_folds']
    create_output_logs = parameters['create_output_logs']
    output_suffix = parameters['output_suffix']
    generate_plots = parameters['generate_plots']
    
    print(f"Configuration:")
    print(f"  - Index well ID: {index_well_id}")
    print(f"  - Number of input wells: {len(input_wells_ids)}")
    print(f"  - Number of input logs: {len(input_logs_ids)}")
    print(f"  - Target log ID: {target_log_id}")
    print(f"  - Imputation mode: {['Missing Values Only', 'Cross-Validation Test', 'Full Re-prediction'][imputation_mode]}")
    print(f"  - Use depth feature: {use_depth_feature}")
    print(f"  - Advanced models enabled: {enable_advanced_models}")
    print(f"  - Cross validation: {cross_validation}")
    if cross_validation:
        print(f"  - CV folds: {cv_folds}")
    print(f"  - Create output logs: {create_output_logs}")
    print()

    # Initialize data loader
    data_loader = PetrelDataLoader(petrel)

    # Load data from Petrel
    try:
        combined_data = data_loader.load_wells_and_logs(
            input_wells_ids, input_logs_ids, target_log_id, use_depth_feature
        )

        # Get target log name
        target_log_obj = petrel.get_petrelobjects_by_guids([target_log_id])[0]
        target_log_name = target_log_obj.petrel_name

        # Get feature column names
        feature_cols = [col for col in combined_data.columns
                       if col not in ['WELL', target_log_name]]

        print(f"Target log: {target_log_name}")
        print(f"Feature columns: {feature_cols}")
        print()

    except Exception as e:
        print(f"Error loading data from Petrel: {str(e)}")
        return

    # Get data coverage statistics
    coverage_stats = data_loader.get_data_coverage_stats(combined_data)
    print("Data coverage statistics:")
    for log_name, coverage in coverage_stats.items():
        print(f"  {log_name}: {coverage:.1f}%")
    print()

    # Get index well data for reference
    try:
        index_data = data_loader.identify_index_well_data(
            combined_data, index_well_id, target_log_name
        )
    except Exception as e:
        print(f"Error processing index well: {str(e)}")
        return

    # Initialize ML framework
    print("Initializing ML imputation framework...")
    ml_framework = PetrelMLImputationFramework(
        enable_advanced_models=enable_advanced_models,
        random_seed=42
    )

    available_models = ml_framework.get_available_models()
    print(f"Available models: {list(available_models.keys())}")
    print()

    # Train and evaluate models
    try:
        results = ml_framework.train_and_evaluate_models(
            combined_data, target_log_name, feature_cols,
            test_size=test_percentage/100.0,
            use_cv=cross_validation,
            cv_folds=cv_folds
        )
    except Exception as e:
        print(f"Error training models: {str(e)}")
        return

    # Print results summary
    print("Model Performance Summary")
    print("="*80)

    if results:
        # Sort by test MAE
        valid_results = {k: v for k, v in results.items() if 'error' not in v}

        if valid_results:
            sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['test_mae'])

            print(f"Target Log: {target_log_name}")
            print("-" * 80)
            print(f"{'Model':<20} {'Test MAE':<10} {'Test R²':<10} {'CV MAE':<12} {'Time (s)':<10}")
            print("-" * 80)

            for model_name, metrics in sorted_results:
                cv_mae_str = f"{metrics['cv_mae']:.3f}" if metrics['cv_mae'] is not None else "N/A"
                print(f"{model_name:<20} {metrics['test_mae']:<10.3f} {metrics['test_r2']:<10.3f} "
                      f"{cv_mae_str:<12} {metrics['training_time']:<10.2f}")

            # Get best model
            best_model_name = sorted_results[0][0]
            best_metrics = sorted_results[0][1]

            print(f"\nBest model: {best_model_name} "
                  f"(MAE: {best_metrics['test_mae']:.3f}, R²: {best_metrics['test_r2']:.3f})")
            print("="*80)

            # Perform imputation based on mode
            if imputation_mode == 0:  # Missing Values Only
                print("\nPerforming imputation on missing values...")
                imputed_data = perform_imputation(
                    combined_data, target_log_name, feature_cols,
                    best_metrics['model'], best_metrics['imputer']
                )

                # Create output logs in Petrel if requested
                if create_output_logs:
                    create_petrel_output_logs(
                        petrel, imputed_data, target_log_name,
                        input_wells_ids, output_suffix
                    )

            elif imputation_mode == 1:  # Cross-Validation Test
                print("\nCross-validation test mode - no new logs created")
                print("Results show model performance on held-out data")

            elif imputation_mode == 2:  # Full Re-prediction
                print("\nPerforming full re-prediction...")
                # Re-predict all values including existing ones
                full_prediction_data = perform_full_prediction(
                    combined_data, target_log_name, feature_cols,
                    best_metrics['model'], best_metrics['imputer']
                )

                if create_output_logs:
                    create_petrel_output_logs(
                        petrel, full_prediction_data, target_log_name,
                        input_wells_ids, output_suffix + "_predicted"
                    )

            # Generate plots if requested
            if generate_plots:
                print("\nGenerating visualization plots...")
                create_analysis_plots(combined_data, results, target_log_name)

        else:
            print("No valid model results to display")

    else:
        print("No model results available")

    print("\n" + "="*80)
    print("PETREL INTERACTIVE IMPUTATION COMPLETED")
    print("="*80)
    print(f"Execution completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def perform_imputation(df: pd.DataFrame, target_col: str, feature_cols: List[str],
                      model: Any, imputer: SimpleImputer) -> pd.DataFrame:
    """Perform imputation on missing values only"""

    df_imputed = df.copy()
    missing_mask = df_imputed[target_col].isna()

    if not missing_mask.any():
        print("  No missing values found to impute")
        return df_imputed

    # Prepare features for missing samples
    X_missing = df_imputed.loc[missing_mask, feature_cols].copy()
    X_missing_imputed = imputer.transform(X_missing)

    # Make predictions
    predictions = model.predict(X_missing_imputed)

    # Fill missing values
    df_imputed.loc[missing_mask, target_col] = predictions

    print(f"  Imputed {missing_mask.sum()} missing values")

    return df_imputed


def perform_full_prediction(df: pd.DataFrame, target_col: str, feature_cols: List[str],
                           model: Any, imputer: SimpleImputer) -> pd.DataFrame:
    """Perform full re-prediction of all values"""

    df_predicted = df.copy()

    # Prepare all features
    X_all = df_predicted[feature_cols].copy()
    X_all_imputed = imputer.transform(X_all)

    # Make predictions for all samples
    predictions = model.predict(X_all_imputed)

    # Create new column with predictions
    df_predicted[f'{target_col}_predicted'] = predictions

    print(f"  Generated predictions for {len(df_predicted)} samples")

    return df_predicted


def create_petrel_output_logs(petrel: PetrelConnection, data: pd.DataFrame,
                             target_log_name: str, well_ids: List[str], suffix: str):
    """Create new well logs in Petrel with imputed/predicted values"""

    print("  Creating output logs in Petrel...")

    try:
        # Get well objects
        wells = petrel.get_petrelobjects_by_guids(well_ids)

        # Get target log for cloning
        target_logs = [log for log in petrel.global_well_logs
                      if log.petrel_name == target_log_name]

        if not target_logs:
            print(f"    Error: Could not find global well log '{target_log_name}' for cloning")
            return

        target_global_log = target_logs[0]
        output_log_name = target_log_name + suffix

        # Create new global well log by cloning
        try:
            new_global_log = target_global_log.clone(output_log_name, copy_values=False)
            print(f"    Created new global well log: {output_log_name}")
        except Exception as e:
            print(f"    Using existing global well log: {output_log_name}")
            new_global_log = [log for log in petrel.global_well_logs
                             if log.petrel_name == output_log_name][0]

        # Create well logs for each well
        for well in wells:
            well_name = well.petrel_name
            well_data = data[data['WELL'] == well_name]

            if well_data.empty:
                continue

            try:
                # Create or get well log
                try:
                    well_log = new_global_log.create_well_log(well)
                except:
                    # Log might already exist
                    well_log = new_global_log.log(well_name)

                # Prepare data
                md_values = well_data['MD'].values if 'MD' in well_data.columns else well_data.index.values

                # Use imputed values if available, otherwise original values
                if f'{target_log_name}_predicted' in well_data.columns:
                    log_values = well_data[f'{target_log_name}_predicted'].values
                else:
                    log_values = well_data[target_log_name].values

                # Set values
                well_log.readonly = False
                well_log.set_values(md_values.tolist(), log_values.tolist())

                print(f"    Updated well log for: {well_name}")

            except Exception as e:
                print(f"    Error updating well log for {well_name}: {str(e)}")

        print(f"  Successfully created output logs with suffix: {suffix}")

    except Exception as e:
        print(f"  Error creating output logs: {str(e)}")


def create_analysis_plots(data: pd.DataFrame, results: Dict, target_log_name: str):
    """Create analysis and visualization plots"""

    try:
        # Set up plotting
        plt.style.use('default')
        if SEABORN_AVAILABLE:
            sns.set_palette("husl")

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'Missing Log Imputation Analysis - {target_log_name}',
                     fontsize=16, fontweight='bold')

        # Plot 1: Model performance comparison
        ax1 = axes[0, 0]
        valid_results = {k: v for k, v in results.items() if 'error' not in v}

        if valid_results:
            model_names = list(valid_results.keys())
            test_maes = [valid_results[name]['test_mae'] for name in model_names]

            bars = ax1.bar(model_names, test_maes, alpha=0.7)
            ax1.set_title('Model Performance (Test MAE)')
            ax1.set_ylabel('Mean Absolute Error')
            ax1.tick_params(axis='x', rotation=45)

            # Add value labels
            for bar, mae in zip(bars, test_maes):
                ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(test_maes)*0.01,
                        f'{mae:.1f}', ha='center', va='bottom')

        # Plot 2: Data coverage by well
        ax2 = axes[0, 1]
        log_columns = [col for col in data.columns if col not in ['WELL', 'MD']]

        coverage_by_well = []
        for well in data['WELL'].unique():
            well_data = data[data['WELL'] == well]
            well_coverage = []
            for col in log_columns:
                coverage = (1 - well_data[col].isna().mean()) * 100
                well_coverage.append(coverage)
            coverage_by_well.append(well_coverage)

        if coverage_by_well:
            im = ax2.imshow(coverage_by_well, cmap='RdYlGn', aspect='auto', vmin=0, vmax=100)
            ax2.set_title('Data Coverage by Well (%)')
            ax2.set_xlabel('Log Type')
            ax2.set_ylabel('Well')
            ax2.set_xticks(range(len(log_columns)))
            ax2.set_xticklabels(log_columns, rotation=45)
            ax2.set_yticks(range(len(data['WELL'].unique())))
            ax2.set_yticklabels(data['WELL'].unique(), fontsize=8)

            # Add colorbar
            cbar = plt.colorbar(im, ax=ax2)
            cbar.set_label('Coverage (%)')

        # Plot 3: Target log distribution
        ax3 = axes[1, 0]
        target_values = data[target_log_name].dropna()
        if not target_values.empty:
            ax3.hist(target_values, bins=30, alpha=0.7, edgecolor='black')
            ax3.set_title(f'{target_log_name} Distribution')
            ax3.set_xlabel(target_log_name)
            ax3.set_ylabel('Frequency')

        # Plot 4: Missing data pattern
        ax4 = axes[1, 1]
        missing_counts = []
        well_names = []
        for well in data['WELL'].unique():
            well_data = data[data['WELL'] == well]
            missing_count = well_data[target_log_name].isna().sum()
            missing_counts.append(missing_count)
            well_names.append(well)

        bars = ax4.bar(range(len(well_names)), missing_counts, alpha=0.7)
        ax4.set_title(f'Missing {target_log_name} Values by Well')
        ax4.set_xlabel('Well')
        ax4.set_ylabel('Missing Values Count')
        ax4.set_xticks(range(len(well_names)))
        ax4.set_xticklabels(well_names, rotation=45, fontsize=8)

        plt.tight_layout()

        # Save plot
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'petrel_imputation_analysis_{target_log_name}_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"    Analysis plot saved as: {filename}")

        plt.show()

    except Exception as e:
        print(f"  Error creating plots: {str(e)}")


if __name__ == "__main__":
    main()
